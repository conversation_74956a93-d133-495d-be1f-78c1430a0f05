//
//  TestFittingPathGenerator.swift
//  PathGenerator
//
//  Created by AI Assistant
//  测试新的FittingPathGenerator
//

import Foundation
import UIKit

// 简单的测试函数
func testFittingPathGenerator() {
    print("开始测试 FittingPathGenerator...")
    
    // 创建参数配置
    var params = Params()
    params.calligraphyMinLineWidth = 2.0
    params.calligraphyMaxForce = 1.0
    params.calligraphyMinForce = 0.1
    params.radius = 5.0
    
    // 创建渐变配置
    let taperConfig = TaperConfig(isEnable: false, startLength: 0, endLength: 0)
    
    // 创建路径生成器
    let pathGenerator = FittingPathGenerator(params: params, taperConfig: taperConfig)
    
    // 创建测试点
    let point1 = ZDPointUnit(location: CGPoint(x: 10, y: 10), force: 0.5, radius: 3.0)
    let point2 = ZDPointUnit(location: CGPoint(x: 20, y: 20), force: 0.7, radius: 4.0)
    let point3 = ZDPointUnit(location: CGPoint(x: 30, y: 15), force: 0.6, radius: 3.5)
    
    // 测试路径生成
    print("测试开始绘制...")
    let effectArea1 = pathGenerator.began(point: point1, touches: nil, event: nil)
    print("开始点效果区域: \(effectArea1)")
    
    print("测试推进绘制...")
    let effectArea2 = pathGenerator.push(point: point2, touches: nil, event: nil)
    print("推进点效果区域: \(effectArea2)")
    
    print("测试结束绘制...")
    let effectArea3 = pathGenerator.end(point: point3, touches: nil, event: nil)
    print("结束点效果区域: \(effectArea3)")
    
    // 获取生成的路径
    if let path = pathGenerator.pathRef() {
        print("成功生成路径，边界框: \(path.boundingBox)")
    } else {
        print("路径生成失败")
    }
    
    // 获取可用点
    let availablePoints = pathGenerator.avaliablePoints()
    print("可用点数量: \(availablePoints.count)")
    
    print("FittingPathGenerator 测试完成！")
}

// 测试工具类
func testMathUtils() {
    print("开始测试 MathUtils...")
    
    let pointA = CGPoint(x: 0, y: 0)
    let pointB = CGPoint(x: 10, y: 10)
    let interval: TimeInterval = 0.1
    
    let speed = MathUtils.getSpeedBetweenTwoPointsWithTimeInterval(pointA: pointA, pointB: pointB, interval: interval)
    print("计算速度: \(speed)")
    
    let force = MathUtils.newBrushTransformSpeedToForce(speed, 1.0, 0.1, 1.0, fasterIsThick: true)
    print("转换压力: \(force)")
    
    print("MathUtils 测试完成！")
}

// 测试ZDPathUtils
func testZDPathUtils() {
    print("开始测试 ZDPathUtils...")
    
    let center = CGPoint(x: 50, y: 50)
    let radius: CGFloat = 10
    
    let circlePath = ZDPathUtils.generateCircle(center: center, radius: radius)
    print("生成圆形路径，边界框: \(circlePath.boundingBox)")
    
    let points = [CGPoint(x: 0, y: 0), CGPoint(x: 10, y: 10), CGPoint(x: 20, y: 0)]
    let closePath = ZDPathUtils.generateNormalClosePath(points: points)
    print("生成闭合路径，边界框: \(closePath.boundingBox)")
    
    print("ZDPathUtils 测试完成！")
}

// 运行所有测试
func runAllTests() {
    print("=== 开始运行所有测试 ===")
    testMathUtils()
    print("")
    testZDPathUtils()
    print("")
    testFittingPathGenerator()
    print("=== 所有测试完成 ===")
}
